import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as colors
from matplotlib.colors import LinearSegmentedColormap

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def read_charging_data(filename):
    """读取充电数据Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        print("Excel文件读取成功！")
        print("数据形状:", df.shape)
        return df
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def parse_charging_data(df):
    """解析充电数据的特殊格式"""
    # 根据列名提取距离信息
    distance_columns = []
    for col in df.columns:
        if '距离' in str(col):
            try:
                distance = float(str(col).replace('距离', ''))
                distance_columns.append((distance, col))
            except:
                continue
    
    distance_columns.sort()  # 按距离排序
    print(f"找到距离列: {[d[0] for d in distance_columns]}")
    
    # 创建结构化数据
    structured_data = []
    
    for distance, dist_col in distance_columns:
        col_idx = df.columns.get_loc(dist_col)
        
        # 第0行是表头，第1行开始是数据
        for row_idx in range(1, len(df)):
            offset_val = df.iloc[row_idx, col_idx]      # 偏移值
            time_val = df.iloc[row_idx, col_idx + 1]    # 时间值
            
            # 跳过空值或无效值
            if (pd.isna(offset_val) or pd.isna(time_val) or 
                time_val == '/' or str(time_val) == 'nan' or 
                offset_val == '/' or str(offset_val) == 'nan' or
                time_val == 'NaN' or offset_val == 'NaN'):
                continue
            
            try:
                x = float(distance)
                z = float(offset_val)  # 横纵偏移作为z坐标
                t = float(time_val)
                
                if t > 0:  # 只接受正的时间值
                    structured_data.append({
                        'x': x,
                        'z': z,
                        'time': t
                    })
            except Exception as e:
                continue
    
    print(f"总共解析得到 {len(structured_data)} 个有效数据点")
    return pd.DataFrame(structured_data)

def calculate_charging_parameters(df):
    """计算充电参数"""
    # 根据用户提供的公式计算
    C = 0.05  # 电容值 (F) - 50mF = 0.05F
    V = 1.2   # 电压值 (V)
    
    # 计算一次充电所存储的能量 (E)
    E = 0.5 * C * V**2  # 焦耳
    print(f"一次充电存储的能量 E = {E:.6f} 焦耳 (电容: {C*1000}mF, 电压: {V}V)")
    
    # 计算充电系数（平均充电功率）
    charging_coefficient = []
    for idx, row in df.iterrows():
        time_val = row['time']
        
        # P_avg (毫瓦) = (E / t) * 1000
        if time_val > 0:
            coeff = (E / time_val) * 1000
        else:
            coeff = 0
        
        charging_coefficient.append(coeff)
    
    df['charging_coeff'] = charging_coefficient
    print(f"充电系数计算完成，范围: {min(charging_coefficient):.3f} - {max(charging_coefficient):.3f} 毫瓦")
    
    return df, E

def create_symmetric_grid(df, x_range=(0, 3.5), z_range=(-1.25, 1.25), step=0.25):
    """创建对齐的对称网格数据"""
    
    # 创建网格 - 确保数据点位于网格块的中心
    x_values = np.arange(x_range[0], x_range[1] + step, step)
    z_values = np.arange(z_range[0], z_range[1] + step, step)
    
    X, Z = np.meshgrid(x_values, z_values)
    charging_matrix = np.zeros_like(X)
    
    print(f"创建网格: X范围 {x_range}, Z范围 {z_range}, 步长 {step}")
    print(f"网格大小: {X.shape}")
    
    # 填充数据 - 数据点应该对应网格块的中心
    for idx, row in df.iterrows():
        x_coord = row['x']
        z_coord = row['z']
        coeff = row['charging_coeff']
        
        # 找到最近的网格点（数据点对应的网格块）
        x_idx = np.argmin(np.abs(x_values - x_coord))
        z_idx = np.argmin(np.abs(z_values - z_coord))
        
        # 确保索引在有效范围内
        if 0 <= x_idx < len(x_values) and 0 <= z_idx < len(z_values):
            charging_matrix[z_idx, x_idx] = coeff
            
            # 对称复制到负z值（如果z>0的话）
            if z_coord > 0:
                z_neg_coord = -z_coord
                z_neg_idx = np.argmin(np.abs(z_values - z_neg_coord))
                if 0 <= z_neg_idx < len(z_values) and z_neg_idx != z_idx:
                    charging_matrix[z_neg_idx, x_idx] = coeff
    
    return X, Z, charging_matrix, x_values, z_values

def plot_2d_colormap(X, Z, charging_matrix, x_values, z_values):
    """创建2D colormap图，显示充电系数数值"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 创建自定义colormap（浅色到深色）
    colors_list = ['white', 'lightblue', 'blue', 'darkblue', 'navy']
    cmap = LinearSegmentedColormap.from_list('charging', colors_list, N=256)
    
    # 创建网格边界用于pcolormesh
    step = 0.25
    x_edges = np.arange(min(x_values) - step/2, max(x_values) + step, step)
    z_edges = np.arange(min(z_values) - step/2, max(z_values) + step, step)
    
    # 使用pcolormesh绘制对齐的方块
    im = ax.pcolormesh(x_edges, z_edges, charging_matrix, cmap=cmap, shading='flat', edgecolors='black', linewidth=0.5)
    
    # 在每个方块中心显示充电系数数值
    for i in range(len(z_values)):
        for j in range(len(x_values)):
            coeff = charging_matrix[i, j]
            if coeff > 0:  # 只显示非零值
                # 方块中心位置
                x_center = x_values[j]
                z_center = z_values[i]
                
                # 根据充电系数值选择文字颜色（深色背景用白字，浅色背景用黑字）
                normalized_coeff = coeff / np.max(charging_matrix) if np.max(charging_matrix) > 0 else 0
                text_color = 'white' if normalized_coeff > 0.5 else 'black'
                
                # 显示充电系数值
                ax.text(x_center, z_center, f'{coeff:.1f}', 
                       ha='center', va='center', fontsize=8, color=text_color, weight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('充电系数 (毫瓦)', fontsize=12)
    
    # 设置标签和标题
    ax.set_xlabel('X轴距离 (m)', fontsize=12)
    ax.set_ylabel('Z轴偏移 (m)', fontsize=12)
    ax.set_title('2D充电系数分布图', fontsize=14)
    
    # 设置刻度但不显示网格线
    ax.set_xticks(x_values)
    ax.set_yticks(z_values)
    
    # 在发射器原点添加红色星星标记
    ax.plot(0, 0, marker='*', color='red', markersize=15, markeredgecolor='darkred', 
           markeredgewidth=2, label='发射器原点', zorder=10)
    
    # 添加图例
    ax.legend(loc='upper right')
    
    # 设置正方形方块
    ax.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig('2d_charging_map.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_3d_colormap(X, Z, charging_matrix, x_values, z_values):
    """创建3D colormap图，整个空间按0.25离散成小正方体"""
    fig = plt.figure(figsize=(16, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    # 创建简单的蓝色深浅变化
    colors_list = ['white', 'lightblue', 'blue', 'darkblue']
    cmap = LinearSegmentedColormap.from_list('charging', colors_list, N=256)
    
    # 获取最大充电系数用于归一化
    max_coeff = np.max(charging_matrix)
    min_coeff = np.min(charging_matrix[charging_matrix > 0])  # 最小非零值
    
    print(f"充电系数范围: {min_coeff:.3f} - {max_coeff:.3f}")
    
    # 3D空间离散参数
    step = 0.25
    cube_size = step  # 正方体大小与网格步长一致，确保紧挨着
    
    # 创建Y轴范围（对称扩展）
    y_range = (-1.25, 1.25)
    y_values = np.arange(y_range[0], y_range[1] + step, step)
    
    print(f"3D网格: X({len(x_values)}) × Y({len(y_values)}) × Z({len(z_values)})")
    
    # 在3D空间中放置正方体
    for i, x_coord in enumerate(x_values):      # X轴：发射器距离
        for j, y_coord in enumerate(y_values):  # Y轴：横向偏移（对称）
            for k, z_coord in enumerate(z_values):  # Z轴：纵向偏移（对称）
                
                # 计算当前点到YZ平面原点的径向距离
                radial_distance = np.sqrt(y_coord**2 + z_coord**2)
                
                # 在原始2D数据中找到对应的充电系数
                # 通过径向距离在z_values中找到最接近的值
                closest_z_idx = np.argmin([abs(abs(z_val) - radial_distance) for z_val in z_values])
                
                if closest_z_idx < len(z_values) and i < len(x_values):
                    coeff = charging_matrix[closest_z_idx, i]
                    
                    # 只有当原始2D数据有值且径向距离在合理范围内时才显示
                    max_radius = max([abs(z) for z in z_values])
                    
                    if coeff > 0 and radial_distance <= max_radius:
                        # 归一化充电系数
                        normalized_coeff = (coeff - min_coeff) / (max_coeff - min_coeff) if max_coeff > min_coeff else 0
                        color = cmap(normalized_coeff)
                        
                        # 调整透明度，充电小的更透明一些，整体更透明
                        alpha = min(0.6, normalized_coeff * 0.4 + 0.02)
                        
                        # 在3D空间中放置正方体，确保对齐到网格
                        ax.bar3d(x_coord - cube_size/2, 
                                y_coord - cube_size/2, 
                                z_coord - cube_size/2,
                                cube_size, cube_size, cube_size,
                                color=color, alpha=alpha, 
                                edgecolor='gray', linewidth=0.1)
    
    # 设置标签和标题
    ax.set_xlabel('X轴距离 (m)', fontsize=12)
    ax.set_ylabel('Y轴偏移 (m)', fontsize=12)
    ax.set_zlabel('Z轴偏移 (m)', fontsize=12)
    ax.set_title('3D充电系数分布图\n', fontsize=14)
    
    # 设置坐标范围
    ax.set_xlim(min(x_values) - step/2, max(x_values) + step/2)
    ax.set_ylim(min(y_values) - step/2, max(y_values) + step/2)
    ax.set_zlim(min(z_values) - step/2, max(z_values) + step/2)
    
    # 设置坐标刻度
    ax.set_xticks(x_values)
    ax.set_yticks(y_values)
    ax.set_zticks(z_values)
    
    # 添加从原点放射出的xyz坐标系线和箭头
    origin = [0, 0, 0]
    
    # 根据数据范围设置坐标轴长度
    x_axis_length = max(x_values)  # X轴到最大值
    y_axis_max = max(y_values)     # Y轴正方向长度
    y_axis_min = min(y_values)     # Y轴负方向长度
    z_axis_max = max(z_values)     # Z轴正方向长度  
    z_axis_min = min(z_values)     # Z轴负方向长度
    
    # X轴箭头（红色）
    ax.quiver(origin[0], origin[1], origin[2], x_axis_length, 0, 0, 
             color='red', linewidth=1, alpha=0.9, arrow_length_ratio=0.05, label='X轴')
    
    # Y轴箭头（绿色） - 正负两个方向
    ax.quiver(origin[0], origin[1], origin[2], 0, y_axis_max, 0, 
             color='green', linewidth=1, alpha=0.9, arrow_length_ratio=0.1, label='Y轴')
    ax.quiver(origin[0], origin[1], origin[2], 0, y_axis_min, 0, 
             color='green', linewidth=1, alpha=0.9, arrow_length_ratio=0.1)
    
    # Z轴箭头（蓝色） - 正负两个方向
    ax.quiver(origin[0], origin[1], origin[2], 0, 0, z_axis_max, 
             color='blue', linewidth=1, alpha=0.9, arrow_length_ratio=0.1, label='Z轴')
    ax.quiver(origin[0], origin[1], origin[2], 0, 0, z_axis_min, 
             color='blue', linewidth=1, alpha=0.9, arrow_length_ratio=0.1)
    
    # 在发射器原点添加红色星星标记
    ax.scatter(0, 0, 0, marker='*', color='red', s=200, edgecolors='darkred', 
              linewidth=2, label='发射器原点', zorder=100)
    
    # 添加图例
    ax.legend(loc='upper right')
    
    plt.tight_layout()
    plt.savefig('3d_charging_map.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    filename = "记录1.xlsx"
    
    # 1. 读取Excel文件
    print("=== 读取Excel文件 ===")
    df_raw = read_charging_data(filename)
    if df_raw is None:
        return
    
    # 2. 解析数据结构
    print("\n=== 解析数据结构 ===")
    df_structured = parse_charging_data(df_raw)
    print(f"解析得到 {len(df_structured)} 个有效数据点")
    
    # 3. 计算充电参数
    print("\n=== 计算充电参数 ===")
    df_processed, energy = calculate_charging_parameters(df_structured)
    
    # 4. 创建对称网格
    print("\n=== 创建对称网格 ===")
    X, Z, charging_matrix, x_values, z_values = create_symmetric_grid(df_processed)
    
    print(f"网格中非零充电系数的位置数: {np.count_nonzero(charging_matrix)}")
    
    # 5. 创建2D可视化
    print("\n=== 创建2D colormap图 ===")
    plot_2d_colormap(X, Z, charging_matrix, x_values, z_values)
    
    # 6. 创建3D可视化
    print("\n=== 创建3D colormap图 ===")
    plot_3d_colormap(X, Z, charging_matrix, x_values, z_values)
    
    # 7. 保存处理后的数据
    df_processed.to_excel("处理后的充电数据.xlsx", index=False)
    print("\n=== 完成 ===")
    print("处理后的数据已保存到 '处理后的充电数据.xlsx'")
    print("2D图表已保存为 '2d_charging_map.png'")
    print("3D图表已保存为 '3d_charging_map.png'")
    print("\n无线充电发射器充电系数分析完成！")

if __name__ == "__main__":
    main()
