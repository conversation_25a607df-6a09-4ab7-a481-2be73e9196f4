try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    print("pandas不可用，将使用替代方法读取Excel文件")

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.colors as colors
from matplotlib.colors import LinearSegmentedColormap

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def read_charging_data(filename):
    """读取充电数据Excel文件"""
    if PANDAS_AVAILABLE:
        try:
            # 读取Excel文件
            df = pd.read_excel(filename)
            print("Excel文件读取成功！")
            print("数据形状:", df.shape)
            print("列名:", df.columns.tolist())
            print("前几行数据:")
            print(df.head())
            return df
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return None
    else:
        # 使用openpyxl作为替代方案
        try:
            from openpyxl import load_workbook
            wb = load_workbook(filename)
            ws = wb.active

            # 读取所有数据
            data = []
            for row in ws.iter_rows(values_only=True):
                data.append(row)

            print("Excel文件读取成功！")
            print("数据行数:", len(data))
            if data:
                print("列数:", len(data[0]) if data[0] else 0)
                print("第一行（表头）:", data[0])
                if len(data) > 1:
                    print("第二行数据:", data[1])

            # 创建简单的数据结构
            if not data:
                return None

            headers = data[0]
            rows = data[1:]

            # 创建类似DataFrame的结构
            class SimpleDataFrame:
                def __init__(self, data, columns):
                    self.data = data
                    self.columns = columns
                    self.shape = (len(data), len(columns))

                def iloc(self, row_idx, col_idx):
                    if row_idx < len(self.data) and col_idx < len(self.data[row_idx]):
                        return self.data[row_idx][col_idx]
                    return None

                def __len__(self):
                    return len(self.data)

            return SimpleDataFrame(rows, headers)

        except ImportError:
            print("需要安装openpyxl库来读取Excel文件")
            return None
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return None

def parse_charging_data_z_section(df):
    """解析Z轴剖面的充电数据"""
    # 根据列名提取距离信息
    distance_columns = []
    for col in df.columns:
        if '距离' in str(col):
            try:
                distance = float(str(col).replace('距离', ''))
                distance_columns.append((distance, col))
            except:
                continue
    
    distance_columns.sort()  # 按距离排序
    print(f"找到距离列: {[d[0] for d in distance_columns]}")
    
    # 创建结构化数据
    structured_data = []
    
    for distance, dist_col in distance_columns:
        col_idx = df.columns.get_loc(dist_col)
        
        # 第0行是表头，第1行开始是数据
        for row_idx in range(1, len(df)):
            offset_val = df.iloc[row_idx, col_idx]      # 偏移值（现在是x方向）
            time_val = df.iloc[row_idx, col_idx + 1]    # 时间值
            
            # 跳过空值或无效值
            if (pd.isna(offset_val) or pd.isna(time_val) or 
                time_val == '/' or str(time_val) == 'nan' or 
                offset_val == '/' or str(offset_val) == 'nan' or
                time_val == 'NaN' or offset_val == 'NaN'):
                continue
            
            try:
                y = float(distance)      # y轴：发射器距离
                x = float(offset_val)    # x轴：横向偏移（可以为负）
                t = float(time_val)
                
                if t > 0:  # 只接受正的时间值
                    structured_data.append({
                        'x': x,  # x轴偏移（可以为负）
                        'y': y,  # y轴距离
                        'time': t
                    })
            except Exception as e:
                continue
    
    print(f"总共解析得到 {len(structured_data)} 个有效数据点")
    return pd.DataFrame(structured_data)

def calculate_charging_parameters(df):
    """计算充电参数"""
    # 根据用户提供的公式计算
    C = 0.05  # 电容值 (F) - 50mF = 0.05F
    V = 1.2   # 电压值 (V)
    
    # 计算一次充电所存储的能量 (E)
    E = 0.5 * C * V**2  # 焦耳
    print(f"一次充电存储的能量 E = {E:.6f} 焦耳 (电容: {C*1000}mF, 电压: {V}V)")
    
    # 计算充电系数（平均充电功率）
    charging_coefficient = []
    for idx, row in df.iterrows():
        time_val = row['time']
        
        # P_avg (毫瓦) = (E / t) * 1000
        if time_val > 0:
            coeff = (E / time_val) * 1000
        else:
            coeff = 0
        
        charging_coefficient.append(coeff)
    
    df['charging_coeff'] = charging_coefficient
    print(f"充电系数计算完成，范围: {min(charging_coefficient):.3f} - {max(charging_coefficient):.3f} 毫瓦")
    
    return df, E

def create_z_section_grid(df, step=0.3):
    """创建Z轴剖面的网格数据，步长改为0.3m"""
    
    # 获取数据范围
    x_min, x_max = df['x'].min(), df['x'].max()
    y_min, y_max = df['y'].min(), df['y'].max()
    
    print(f"数据范围: X({x_min:.2f} - {x_max:.2f}), Y({y_min:.2f} - {y_max:.2f})")
    
    # 扩展范围以包含所有数据点，并确保包含原点
    x_range = (min(x_min, 0) - step, max(x_max, 0) + step)
    y_range = (0, y_max + step)  # y从0开始（发射器位置）
    
    # 创建网格 - 确保数据点位于网格块的中心
    x_values = np.arange(x_range[0], x_range[1] + step, step)
    y_values = np.arange(y_range[0], y_range[1] + step, step)
    
    X, Y = np.meshgrid(x_values, y_values)
    charging_matrix = np.zeros_like(X)
    
    print(f"创建网格: X范围 {x_range}, Y范围 {y_range}, 步长 {step}")
    print(f"网格大小: {X.shape}")
    
    # 填充数据
    for idx, row in df.iterrows():
        x_coord = row['x']
        y_coord = row['y']
        coeff = row['charging_coeff']
        
        # 找到最近的网格点
        x_idx = np.argmin(np.abs(x_values - x_coord))
        y_idx = np.argmin(np.abs(y_values - y_coord))
        
        # 确保索引在有效范围内
        if 0 <= x_idx < len(x_values) and 0 <= y_idx < len(y_values):
            charging_matrix[y_idx, x_idx] = coeff
    
    return X, Y, charging_matrix, x_values, y_values

def plot_2d_colormap_z_section(X, Y, charging_matrix, x_values, y_values):
    """创建Z轴剖面的2D colormap图"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 创建自定义colormap（浅色到深色）
    colors_list = ['white', 'lightblue', 'blue', 'darkblue', 'navy']
    cmap = LinearSegmentedColormap.from_list('charging', colors_list, N=256)
    
    # 创建网格边界用于pcolormesh
    step = 0.3
    x_edges = np.arange(min(x_values) - step/2, max(x_values) + step, step)
    y_edges = np.arange(min(y_values) - step/2, max(y_values) + step, step)
    
    # 使用pcolormesh绘制对齐的方块
    im = ax.pcolormesh(x_edges, y_edges, charging_matrix, cmap=cmap, shading='flat', edgecolors='black', linewidth=0.5)
    
    # 在每个方块中心显示充电系数数值
    for i in range(len(y_values)):
        for j in range(len(x_values)):
            coeff = charging_matrix[i, j]
            if coeff > 0:  # 只显示非零值
                # 方块中心位置
                x_center = x_values[j]
                y_center = y_values[i]
                
                # 根据充电系数值选择文字颜色
                normalized_coeff = coeff / np.max(charging_matrix) if np.max(charging_matrix) > 0 else 0
                text_color = 'white' if normalized_coeff > 0.5 else 'black'
                
                # 显示充电系数值
                ax.text(x_center, y_center, f'{coeff:.1f}', 
                       ha='center', va='center', fontsize=8, color=text_color, weight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax)
    cbar.set_label('充电系数 (毫瓦)', fontsize=12)
    
    # 设置标签和标题
    ax.set_xlabel('X轴偏移 (m)', fontsize=12)
    ax.set_ylabel('Y轴距离 (m)', fontsize=12)
    ax.set_title('Z轴剖面充电系数分布图 (步长0.3m)', fontsize=14)
    
    # 设置刻度
    ax.set_xticks(x_values)
    ax.set_yticks(y_values)
    
    # 在发射器原点添加红色星星标记
    ax.plot(0, 0, marker='*', color='red', markersize=15, markeredgecolor='darkred', 
           markeredgewidth=2, label='发射器原点', zorder=10)
    
    # 添加图例
    ax.legend(loc='upper right')
    
    # 设置正方形方块
    ax.set_aspect('equal')
    
    plt.tight_layout()
    plt.savefig('2d_charging_map_z_section.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    filename = "记录2.xlsx"
    
    # 1. 读取Excel文件
    print("=== 读取Excel文件 ===")
    df_raw = read_charging_data(filename)
    if df_raw is None:
        return
    
    # 2. 解析数据结构（Z轴剖面）
    print("\n=== 解析Z轴剖面数据结构 ===")
    df_structured = parse_charging_data_z_section(df_raw)
    print(f"解析得到 {len(df_structured)} 个有效数据点")
    
    if len(df_structured) == 0:
        print("没有有效数据点，请检查数据格式")
        return
    
    # 3. 计算充电参数
    print("\n=== 计算充电参数 ===")
    df_processed, energy = calculate_charging_parameters(df_structured)
    
    # 4. 创建网格（步长0.3m）
    print("\n=== 创建网格 (步长0.3m) ===")
    X, Y, charging_matrix, x_values, y_values = create_z_section_grid(df_processed, step=0.3)
    
    print(f"网格中非零充电系数的位置数: {np.count_nonzero(charging_matrix)}")
    
    # 5. 创建2D可视化
    print("\n=== 创建2D colormap图 ===")
    plot_2d_colormap_z_section(X, Y, charging_matrix, x_values, y_values)
    
    # 6. 保存处理后的数据
    df_processed.to_excel("处理后的充电数据_Z轴剖面.xlsx", index=False)
    print("\n=== 完成 ===")
    print("处理后的数据已保存到 '处理后的充电数据_Z轴剖面.xlsx'")
    print("2D图表已保存为 '2d_charging_map_z_section.png'")
    print("\nZ轴剖面无线充电发射器充电系数分析完成！")

if __name__ == "__main__":
    main()
